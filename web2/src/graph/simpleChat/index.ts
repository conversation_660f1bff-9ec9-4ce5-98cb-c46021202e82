import { StateGraph, END, START, Annotation } from "@langchain/langgraph";
import { SystemMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import type { BaseMessageLike } from "@langchain/core/messages";
import { createModel } from "../designToCode/model/index";

const SimpleChatStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessageLike[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  input: Annotation<string>({
    reducer: (x, y) => y || x,
    default: () => "",
  }),
  output: Annotation<string>({
    reducer: (x, y) => y || x,
    default: () => "",
  }),
});

/**
 * 错误类型枚举
 */
enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 分析错误类型和严重程度
 */
function analyzeError(error: any): { type: ErrorType; isRetryable: boolean; userMessage: string } {
  const errorMessage = error?.message || '';
  const status = error?.status || error?.statusCode;

  // 网络错误 (502, 503, 504)
  if (status === 502 || status === 503 || status === 504 || errorMessage.includes('Bad Gateway') || errorMessage.includes('Service Unavailable')) {
    return {
      type: ErrorType.NETWORK_ERROR,
      isRetryable: true,
      userMessage: '服务暂时不可用，正在尝试使用备用方案...'
    };
  }

  // 速率限制错误 (429)
  if (status === 429 || errorMessage.includes('rate limit') || errorMessage.includes('quota')) {
    return {
      type: ErrorType.RATE_LIMIT_ERROR,
      isRetryable: true,
      userMessage: '请求过于频繁，请稍后再试...'
    };
  }

  // 超时错误
  if (errorMessage.includes('timeout') || errorMessage.includes('TIMEOUT')) {
    return {
      type: ErrorType.TIMEOUT_ERROR,
      isRetryable: true,
      userMessage: '请求超时，正在重试...'
    };
  }

  // API 错误 (4xx)
  if (status >= 400 && status < 500) {
    return {
      type: ErrorType.API_ERROR,
      isRetryable: false,
      userMessage: '请求格式有误，请重新输入您的问题。'
    };
  }

  // 未知错误
  return {
    type: ErrorType.UNKNOWN_ERROR,
    isRetryable: true,
    userMessage: '遇到了一些技术问题，正在尝试解决...'
  };
}

/**
 * 使用备用模型进行重试
 */
async function tryFallbackModel(messages: any[]): Promise<{ success: boolean; response?: any; error?: any }> {
  try {
    console.log("SimpleChat: 尝试使用备用模型 (OpenAI)...");
    const fallbackModel = createModel("openai");
    const responseMessage = await fallbackModel.invoke(messages);

    console.log("SimpleChat: 备用模型响应成功");
    return { success: true, response: responseMessage };
  } catch (fallbackError) {
    console.error("SimpleChat: 备用模型也失败了:", fallbackError);
    return { success: false, error: fallbackError };
  }
}

/**
 * 简单的聊天节点 - 增强错误处理
 */
async function simpleChatNode(state: typeof SimpleChatStateAnnotation.State) {
  console.log("SimpleChat: 处理用户消息...", {
    messagesCount: state.messages.length,
    input: state.input
  });

  // 构建消息列表 - 使用现有的消息历史
  let messages = [...state.messages];

  // 如果没有消息历史，添加系统消息
  if (messages.length === 0) {
    messages.push(new SystemMessage("你是一个友好的AI助手。请简洁地回复用户的问题。"));
  }

  // 如果有额外的输入，添加为用户消息
  if (state.input && state.input.trim()) {
    messages.push(new HumanMessage(state.input));
  }

  console.log(`SimpleChat: 调用模型处理 ${messages.length} 条消息`);

  try {
    // 首先尝试默认模型 (Google Gemini)
    const model = createModel();
    const responseMessage = await model.invoke(messages);

    console.log("SimpleChat: 模型响应成功", {
      responseLength: responseMessage.content?.toString().length || 0
    });

    return {
      messages: [responseMessage],
      output: responseMessage.content?.toString() || "",
    };
  } catch (error) {
    console.error("SimpleChat: 主模型调用失败:", error);

    // 分析错误类型
    const errorAnalysis = analyzeError(error);

    // 如果是可重试的错误，尝试备用模型
    if (errorAnalysis.isRetryable) {
      const fallbackResult = await tryFallbackModel(messages);

      if (fallbackResult.success && fallbackResult.response) {
        console.log("SimpleChat: 备用模型成功处理请求");
        return {
          messages: [fallbackResult.response],
          output: fallbackResult.response.content?.toString() || "",
        };
      }
    }

    // 如果所有尝试都失败，返回用户友好的错误消息
    let errorMessage = errorAnalysis.userMessage;

    // 根据错误类型提供更具体的建议
    switch (errorAnalysis.type) {
      case ErrorType.NETWORK_ERROR:
        errorMessage += "\n\n💡 建议：请检查网络连接，或稍后再试。";
        break;
      case ErrorType.RATE_LIMIT_ERROR:
        errorMessage += "\n\n💡 建议：请等待几分钟后再发送消息。";
        break;
      case ErrorType.TIMEOUT_ERROR:
        errorMessage += "\n\n💡 建议：请尝试发送较短的消息。";
        break;
      case ErrorType.API_ERROR:
        errorMessage += "\n\n💡 建议：请重新组织您的问题并再次尝试。";
        break;
      default:
        errorMessage += "\n\n💡 建议：请稍后重试，或联系技术支持。";
    }

    const errorResponse = new AIMessage(errorMessage);

    return {
      messages: [errorResponse],
      output: errorResponse.content as string,
    };
  }
}

// 构建简单聊天工作流图
const workflow = new StateGraph(SimpleChatStateAnnotation)
  .addNode("chat", simpleChatNode)
  .addEdge(START, "chat")
  .addEdge("chat", END);

// 编译工作流
export const graph = workflow.compile();
graph.name = "simpleChat";

console.log("SimpleChat: 图已创建并编译完成");
