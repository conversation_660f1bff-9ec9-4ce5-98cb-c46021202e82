'use client';

import { useState } from 'react';
import { Setting<PERSON>, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/utils/cn';

export type ModelType = 'google' | 'openai';
export type OpenaiProviderType = 'htsc' | 'openrouter' | 'local';

export interface ModelConfig {
  type: ModelType;
  providerType?: OpenaiProviderType;
}

interface ModelSelectorProps {
  value: ModelConfig;
  onChange: (config: ModelConfig) => void;
  disabled?: boolean;
  className?: string;
}

const MODEL_OPTIONS = [
  {
    type: 'google' as ModelType,
    label: 'Google Gemini',
    description: 'Google Gemini 2.5 Flash',
  },
  {
    type: 'openai' as ModelType,
    providerType: 'htsc' as OpenaiProviderType,
    label: 'HTSC DeepSeek',
    description: 'ht::saas-deepseek-v3',
  },
  {
    type: 'openai' as ModelType,
    providerType: 'openrouter' as OpenaiProviderType,
    label: 'OpenRouter Gemini',
    description: 'google/gemini-2.5-flash',
  },
  {
    type: 'openai' as ModelType,
    providerType: 'local' as OpenaiProviderType,
    label: 'Local Model',
    description: 'gemini-2.5-flash (local)',
  },
];

function getModelLabel(config: ModelConfig): string {
  const option = MODEL_OPTIONS.find(
    opt => opt.type === config.type && 
    (config.type === 'google' || opt.providerType === config.providerType)
  );
  return option?.label || 'Unknown Model';
}

export function ModelSelector({ value, onChange, disabled, className }: ModelSelectorProps) {
  const [open, setOpen] = useState(false);

  const handleSelect = (option: typeof MODEL_OPTIONS[0]) => {
    const newConfig: ModelConfig = {
      type: option.type,
      ...(option.providerType && { providerType: option.providerType }),
    };
    onChange(newConfig);
    setOpen(false);
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          disabled={disabled}
          className={cn(
            "h-8 px-2 text-xs text-gray-600 hover:text-gray-900 hover:bg-gray-100",
            className
          )}
        >
          <Settings className="h-3 w-3 mr-1" />
          {getModelLabel(value)}
          <ChevronDown className="h-3 w-3 ml-1" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>选择模型</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {MODEL_OPTIONS.map((option, index) => (
          <DropdownMenuItem
            key={index}
            onClick={() => handleSelect(option)}
            className="flex flex-col items-start py-2"
          >
            <div className="font-medium">{option.label}</div>
            <div className="text-xs text-gray-500">{option.description}</div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
